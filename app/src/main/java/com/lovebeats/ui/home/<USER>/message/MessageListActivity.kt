package com.lovebeats.ui.home.chat.message

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.database.ChildEventListener
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.GenericTypeIndicator
import com.google.firebase.database.ServerValue
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveChatInfo
import com.lovebeats.firebase.database.FirebaseRetrieveChildListener
import com.lovebeats.firebase.database.FirebaseRetrieveChildListenerGeneric
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.GenderType
import com.lovebeats.models.UserObject
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.home.browseProfiles.OtherUserProfileModal
import com.lovebeats.ui.home.chat.ChatActivity
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment.Companion.DM_REQUEST
import com.lovebeats.ui.home.chat.dialog.ChatUser
import com.lovebeats.utils.AppLogic
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Utils.Companion.getDateFromTimestamp
import com.stfalcon.chatkit.commons.ImageLoader
import com.stfalcon.chatkit.messages.MessageHolders
import com.stfalcon.chatkit.messages.MessageInput
import com.stfalcon.chatkit.messages.MessageInput.InputListener
import com.stfalcon.chatkit.messages.MessagesList
import com.stfalcon.chatkit.messages.MessagesListAdapter
import timber.log.Timber
import java.util.UUID

class MessageListActivity : AppCompatActivity(), MessagesListAdapter.SelectionListener, MessagesListAdapter.OnLoadMoreListener, InputListener,
    MessageInput.AttachmentsListener,
    MessageHolders.ContentChecker<Message>{

    private val senderId = "0"
    private var imageLoader: ImageLoader? = null
    private var messagesAdapter: MessagesListAdapter<Message>? = null
    private var messagesList: MessagesList? = null
    private var context: Context? = null
    private var messageInput: MessageInput? = null
    private var blankMessageUserAvatar: ImageView? = null
    private var blankMessageRelativeLayout: RelativeLayout? = null
    private var user0Map: HashMap<String, String>? = null
    private var user1Map: HashMap<String, String>? = null
    private var firebaseDatabaseUtil: FirebaseDatabaseUtil? = null
    private var conversationId: String? = null
    private var isBlankMessageViewShown = false
    private var mainUserid: String? = null
    private var otherUserId: String? = null

    private var firebaseDatabaseReference: DatabaseReference? = null
    private var firebaseChildEventListener: ChildEventListener? = null

    private var lastMessageId: String? = null
    val localMessagesList = ArrayList<Message>()

    private var unMatchedUserId: String? = null

    private var intentMessage: String? = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_message_list)

        user0Map = intent.getSerializableExtra(Constants.user0) as? HashMap<String, String>?
        user1Map = intent.getSerializableExtra(Constants.user1) as? HashMap<String, String>?

        mainUserid = AccountPreferences.getInstance(context).getStringValue(
            Constants.firebaseUserId, user0Map?.get(
                Constants.firebaseUserId))
        otherUserId = user1Map?.get(Constants.firebaseUserId)
        conversationId = intent.getStringExtra(Constants.conversationId)
        context = this
        firebaseDatabaseUtil = FirebaseDatabaseUtil(this)

        val userNameTextview = findViewById<TextView>(R.id.userName)
        userNameTextview.text = user1Map?.get("name") ?: ""

        imageLoader = ImageLoader { imageView: ImageView?, url: String?, payload: Any? ->
            if (imageView != null) {
                GlideApp.with(this).load(url).apply(RequestOptions.circleCropTransform()).into(imageView)
            }
        }

        messagesList = findViewById(R.id.messagesList)
        val userAvatar = findViewById<ImageView>(R.id.user_avatar)
        val headerBackArrow = findViewById<ImageView>(R.id.account_header_left_image_view)
        blankMessageUserAvatar = findViewById(R.id.message_blank_profile_image)
        blankMessageRelativeLayout = findViewById(R.id.message_blank_view)
        headerBackArrow.setOnClickListener { v: View? -> startChatActivity(null) }
        val toolbar = findViewById<Toolbar>(R.id.messageListToolbar)

        if (toolbar != null) {
            setSupportActionBar(toolbar)
            supportActionBar?.setDisplayShowTitleEnabled(false)
        }
        GlideApp.with(this).load(user1Map?.get(Constants.avatar)).apply(RequestOptions.circleCropTransform()).into(userAvatar)

        messageInput = findViewById(R.id.input)
        messageInput?.setInputListener(this)
        messageInput?.setAttachmentsListener(this)

        initAdapter()
        messagesFromChatId

        userAvatar.setOnClickListener { v: View? -> openOtherUserProfile(otherUserId) }

        if (conversationId != ChatDialogListFragment.CHAT_ID) {

            setMessageReadStatus()
        }

        intentMessage = intent.getStringExtra(ChatDialogListFragment.EXTRA_LAST_MESSAGE)

        if (intentMessage == ChatDialogListFragment.SEND_A_SONG) {
            showBlankMessageBackground()
            messageInput?.findViewById<ImageButton>(com.stfalcon.chatkit.R.id.attachmentButton)?.visibility = View.VISIBLE
        } else {
            messageInput?.findViewById<ImageButton>(com.stfalcon.chatkit.R.id.attachmentButton)?.visibility = View.GONE
        }

        if (intent.getBooleanExtra(DM_REQUEST, false)) {
            val dmMessage = intent.getStringExtra(ChatDialogListFragment.EXTRA_LAST_MESSAGE)
            var messageToSet = ""
            if (dmMessage != null) {
                messageToSet = dmMessage
            }
            setDataInChatNodeFirebase(messageToSet, null, true)
        }
        checkForUnmatchedUsersOfOtherUser()

        if (UserObject.isLoveBeatPlusUser == true) {
            setMessageUnreadListener()
        }
    }

    private fun startChatActivity(unmatchedUserId: String?) {
        val intent = Intent(context, ChatActivity::class.java)
        intent.putExtra("MessageListActivity", true)
        if (unmatchedUserId != null) {
            intent.putExtra(UN_MATCHED_USER_ID, unmatchedUserId)
        }
        startActivity(intent)
    }

    private fun openOtherUserProfile(otherUserId: String?) {
        val intent = Intent(this, OtherUserProfileModal::class.java)
        intent.putExtra(Constants.firebaseUserId, otherUserId)
        intent.putExtra(DM_REQUEST, intent.getBooleanExtra(DM_REQUEST, false))
        startActivity(intent)
    }

    private fun setMessageReadStatus() {
        val lastMessageNodeReference = user1Map?.get(Constants.firebaseUserId)?.let { otherMatchedUserId ->
            mainUserid?.let { it1 ->
                FirebaseDatabase.getInstance().getReference(Constants.connections)
                        .child(it1).child(Constants.matches)
                        .child(otherMatchedUserId)
            }
        }
        if (mainUserid!= null && user1Map?.get(Constants.firebaseUserId) != null) {
            lastMessageNodeReference?.child(Constants.unread)?.setValue(false)
        }
    }

    private fun setMessageUnreadListener() {
        if (mainUserid != null && otherUserId != null) {
            firebaseDatabaseUtil?.readMessageUnreadStatusFromFirebase(object :
                FirebaseRetrieveChildListenerGeneric {
                override fun onSuccess(dataSnapshot: DataSnapshot) {
                    val genericTypeIndicator: GenericTypeIndicator<Map<String?, Any?>?> = object : GenericTypeIndicator<Map<String?, Any?>?>() {}
                    val conversationChatMap = dataSnapshot.getValue(genericTypeIndicator)
                    val conversationUserFirebaseId = conversationChatMap?.get(Constants.createdBy)?.toString()
                    val conversationUserMessage = conversationChatMap?.get(Constants.message)?.toString()
                    val conversationUserTimestamp = conversationChatMap?.get(Constants.timestamp)?.toString()?.toLong()
                    val conversationUserUnread:Boolean? = conversationChatMap?.get(Constants.unread) as? Boolean

                    var status = "Not read"
                    if (conversationUserUnread == false) {
                        status = "Read"
                    }

                    clearLastMessageStatus()

                    if (mainUserid == conversationUserFirebaseId) {
                        // Don't update empty messages
                        if (!conversationUserMessage.isNullOrEmpty()) {
                            val chatUser = ChatUser(senderId, user0Map?.get("name"), user0Map?.get("avatar"))
                            val message = Message(conversationUserMessage.toString(), chatUser, conversationUserMessage,
                                conversationUserTimestamp?.let { getDateFromTimestamp(it) }, status)
                            messagesAdapter?.update(message)
                            lastMessageId = message.id
                        }
                    }

                    setMessageReadStatus()
                }

                override fun onFailure() {
                }
            }, otherUserId = otherUserId!!, mainUserId = mainUserid!!)
        }
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int) {}
    override fun onSelectionChanged(count: Int) {}
    override fun onSubmit(input: CharSequence): Boolean {
        // Don't send empty messages
        if (input.toString().trim().isEmpty()) {
            return false
        }
        setDataInChatNodeFirebase(input, null)
        return true
    }

    private fun canChatProceedFurther(): Boolean {
        if (localMessagesList.isNotEmpty() && localMessagesList.size == 1) {
            val messageSentBy = localMessagesList[0].user.id
            val messageSong = localMessagesList[0].song
            if (messageSentBy.equals(senderId) && messageSong != null) {
                return false
            }
        }
        return true
    }

    private fun showSongSentDialog() {
        val mainUserGender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        val otherUserGender = user1Map?.get(Constants.gender)
        if (mainUserGender == GenderType.man.toString() && otherUserGender == GenderType.woman.toString()) {
            val dialogBuilder = AlertDialog.Builder(this)
            dialogBuilder.setMessage("")
                ?.setCancelable(false)
                ?.setPositiveButton("OK") { dialog, id -> }
            val alert = dialogBuilder.create()
            alert.setMessage(getString(R.string.waiting_message))
            alert.show()
            intentMessage = ""
        }
    }

    private fun setDataInChatNodeFirebase(input: CharSequence, songUrl: String?, dmRequest: Boolean = false) {
        hideBlankMessageBackground()

        clearLastMessageStatus()

        lastMessageId = input.toString()

        //if conversation is null then create a new conversation id under matches node and also under conversations node and assign this
        //new created conversation id to the the conversation id field
        var mFirebaseDatabaseMessages: DatabaseReference?

        if (conversationId == ChatDialogListFragment.CHAT_ID) {

            mFirebaseDatabaseMessages = FirebaseDatabase.getInstance().getReference(Constants.conversations).push()
            conversationId = mFirebaseDatabaseMessages.key
            messagesFromChatId
        }

        mFirebaseDatabaseMessages = conversationId?.let { FirebaseDatabase.getInstance().getReference(
            Constants.conversations).child(it) }

        val map: MutableMap<String, Any?> = HashMap()
        map[Constants.createdBy] = mainUserid
        map[Constants.message] = input.toString()
        map[Constants.timestamp] = ServerValue.TIMESTAMP
        map[Constants.sendTo] = otherUserId
        songUrl?.let {
            map[Constants.songUrl] = it
        }
        mFirebaseDatabaseMessages?.push()?.setValue(map)

        // Setting the message details in other user matches node along with unread = true
        if (mainUserid != null && otherUserId != null) {

            val lastMessageNodeReference = otherUserId?.let {
                mainUserid?.let { it1 ->
                    FirebaseDatabase.getInstance().getReference(Constants.connections)
                        .child(it)
                        .child(Constants.matches)
                        .child(it1)
                }
            }

            val matchesLastMessageMap: MutableMap<String, Any?> = HashMap()
            matchesLastMessageMap[Constants.chatId] = conversationId
            matchesLastMessageMap[Constants.timestamp] = ServerValue.TIMESTAMP

            if (input.toString() == CHAT_FIRST_SONG) {
                matchesLastMessageMap[Constants.lastMessage] = "song"
            } else {
                matchesLastMessageMap[Constants.lastMessage] = input.toString()
            }

            matchesLastMessageMap[Constants.unread] = true
            matchesLastMessageMap[Constants.createdBy] = mainUserid

            if (dmRequest) {
                matchesLastMessageMap[Constants.dmRequest] = false
            }

            lastMessageNodeReference?.updateChildren(matchesLastMessageMap)

            // Setting the message details in other main user matches node
            val lastMessageNodeReferenceOfMainUser = mainUserid?.let {
                otherUserId?.let { it1 ->
                    FirebaseDatabase.getInstance().getReference(Constants.connections)
                        .child(it)
                        .child(Constants.matches)
                        .child(it1)
                }
            }

            val matchesMainUserLastMessageMap: MutableMap<String, Any?> = HashMap()
            matchesMainUserLastMessageMap[Constants.chatId] = conversationId
            matchesMainUserLastMessageMap[Constants.timestamp] = ServerValue.TIMESTAMP

            if (dmRequest) {
                matchesMainUserLastMessageMap[Constants.dmRequest] = false
            }

            if (input.toString() == CHAT_FIRST_SONG) {
                matchesMainUserLastMessageMap[Constants.lastMessage] = "song"
            } else {
                matchesMainUserLastMessageMap[Constants.lastMessage] = input.toString()
            }

            matchesMainUserLastMessageMap[Constants.createdBy] = mainUserid
            matchesMainUserLastMessageMap[Constants.unread] = false
            lastMessageNodeReferenceOfMainUser?.updateChildren(matchesMainUserLastMessageMap)
        }
    }

    private fun checkForUnmatchedUsersOfOtherUser() {
        otherUserId?.let {
            firebaseDatabaseUtil?.getUnmatchedUsersListener(object : FirebaseRetrieveChildListener {
                override fun onChildChanged(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                    firebaseDatabaseReference = reference
                    firebaseChildEventListener = listener
                }

                override fun onChildAdded(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                    if (dataSnapshot.key != null && dataSnapshot.key == mainUserid) {
                        startChatActivity(otherUserId)
                    }
                    firebaseDatabaseReference = reference
                    firebaseChildEventListener = listener
                }

                override fun onChildRemoved(dataSnapshot: DataSnapshot, reference: DatabaseReference, listener: ChildEventListener) {
                    firebaseDatabaseReference = reference
                    firebaseChildEventListener = listener
                }

                override fun onChildMoved(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                    firebaseDatabaseReference = reference
                    firebaseChildEventListener = listener
                }

                override fun onFailure() {}
            }, it)
        }
    }

    private fun initAdapter() {

        val holdersConfig = MessageHolders()
        holdersConfig.setIncomingTextHolder(CustomIncomingMessageViewHolder::class.java)
        holdersConfig.setOutcomingTextHolder(CustomOutComingMessageViewHolder::class.java)
        holdersConfig.setOutcomingTextLayout(R.layout.messages_out_coming_custom_layout)

        holdersConfig.registerContentType(CONTENT_TYPE_VOICE,
            CustomInComingVoiceViewHolder::class.java, R.layout.messages_incoming_voice_layout,
            CustomOutComingVoiceViewHolder::class.java, R.layout.messages_outcoming_voice_layout,
            this)

        messagesAdapter = MessagesListAdapter(senderId, holdersConfig,null)
        messagesAdapter?.setLoadMoreListener(this)
        messagesList?.setAdapter(messagesAdapter, true)
    }

    private val messagesFromChatId: Unit
        get() {

            conversationId?.let {

                firebaseDatabaseUtil?.readChatInfoFromFirebase(object : FirebaseRetrieveChatInfo {

                    override fun onFailure() {
                        Timber.d("Testing getting messages failed")
                    }

                    override fun onSuccess(dataSnapshot: DataSnapshot) {
                        var chatUser: ChatUser

                        val genericTypeIndicator: GenericTypeIndicator<Map<String?, Any?>?> = object : GenericTypeIndicator<Map<String?, Any?>?>() {}
                        val conversationChatMap = dataSnapshot.getValue(genericTypeIndicator)
                        // val conversationUserChatId = dataSnapshot.key?: UUID.randomUUID().toString()
                        val conversationUserFirebaseId = conversationChatMap?.get("createdBy")?.toString()
                        val conversationUserMessage = conversationChatMap?.get("message")?.toString()
                        val conversationUserTimestamp = conversationChatMap?.get(Constants.timestamp)?.toString()?.toLong()
                        val conversationUserSong = conversationChatMap?.get("audioUrl")?.toString()

                        var chatUserId = senderId

                        chatUser = ChatUser(chatUserId, user0Map?.get("name"), user0Map?.get("avatar"))

                        if (mainUserid == conversationUserFirebaseId) {
                            chatUserId = "0"
                            chatUser = ChatUser(chatUserId, user0Map?.get("name"), user0Map?.get("avatar"))
                        } else if (otherUserId == conversationUserFirebaseId) {
                            chatUserId = "1"
                            chatUser = ChatUser(chatUserId, user1Map?.get("name"), user1Map?.get("avatar"))
                        }

                        // Don't display empty messages unless they have a song attachment
                        if (conversationUserMessage.isNullOrEmpty() && conversationUserSong.isNullOrEmpty()) {
                            return
                        }

                        var chatMessageId = conversationUserMessage
                        if (chatMessageId.isNullOrEmpty()) {
                            chatMessageId = UUID.randomUUID().toString()
                        }

                        val message = Message(chatMessageId, chatUser, conversationUserMessage, conversationUserTimestamp?.let { time -> getDateFromTimestamp(time) })
                        if (!conversationUserSong.isNullOrEmpty()) {
                            message.song = Message.Track(conversationUserSong)
                        }
                        localMessagesList.add(message)

                        if (isBlankMessageViewShown) {
                            hideBlankMessageBackground()
                        }
                        messagesAdapter?.addToStart(message, true)
                    }

                    override fun onStart() {
                    }
                }, it)
            }
        }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.chat_screen_more_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        if (mainUserid != null && otherUserId != null) {

            val id = item.itemId
            if (id == R.id.unmatch) {

                val appLogic = AppLogic(this, mainUserid!!, otherUserId!!)
                unMatchedUserId = otherUserId
                appLogic.unMatchUser()
            }
            if (id == R.id.reportUser) {
                val appLogic = AppLogic(this, mainUserid!!, otherUserId!!)
                appLogic.reportUser(true)
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun removeFirebaseListener() {
        firebaseChildEventListener?.let {
            firebaseDatabaseReference?.removeEventListener(it)
        }
    }

    private fun showBlankMessageBackground() {
        isBlankMessageViewShown = true
        blankMessageRelativeLayout?.visibility = View.VISIBLE
        blankMessageUserAvatar?.let { GlideApp.with(this).load(user1Map?.get(Constants.avatar)).apply(RequestOptions.circleCropTransform()).into(it) }
    }

    private fun hideBlankMessageBackground() {
        isBlankMessageViewShown = false
        blankMessageRelativeLayout?.visibility = View.GONE
    }

    private fun clearLastMessageStatus() {
        if (lastMessageId != null && localMessagesList.isNotEmpty()) {
            val message = localMessagesList[localMessagesList.size - 1]
            messagesAdapter?.update(message)
        }
    }

    private fun getVoiceMessage(url: String): Message {
        val message = Message(UUID.randomUUID().toString(), ChatUser(senderId, user0Map?.get("name"), user0Map?.get("avatar")), "", getDateFromTimestamp(System.currentTimeMillis()/1000))
        message.song = Message.Track(url)
        return message
    }

    companion object {
        var UNMATCH = "UNMATCH"
        var REPORT = "REPORT"
        var UN_MATCHED_USER_ID = "UnMatchedUserId"

        const val CONTENT_TYPE_VOICE: Byte = 1
        const val CHAT_SONG_RESULT_CODE = 2
        var CHAT_SONG_URL = "CHAT_SONG_URL"
        var CHAT_FIRST_SONG = "Chat_FIRST_Song"

        var chatPlayer: ChatPlayer? = null
    }

    override fun onAddAttachments() {
        getContent.launch(ChatSongSelectionActivity.newIntent(this))
    }

    override fun hasContentFor(message: Message?, type: Byte): Boolean {
        if (type == CONTENT_TYPE_VOICE) {
            return message?.song != null
                    && message.song?.url != null
        }
        return false
    }

    override fun onPause() {
        super.onPause()
        chatPlayer?.pause()
    }

    private val getContent = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult? ->
        if (result?.resultCode == CHAT_SONG_RESULT_CODE) {
            val url = result.data?.getStringExtra(CHAT_SONG_URL)
            url?.let {
                setDataInChatNodeFirebase(CHAT_FIRST_SONG, it)
                showSongSentDialog()
                messageInput?.findViewById<ImageButton>(com.stfalcon.chatkit.R.id.attachmentButton)?.visibility = View.GONE
            }
        }
    }
}